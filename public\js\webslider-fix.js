/**
 * WebSlider Fix - Ensures slider functionality works properly
 */

(function() {
    'use strict';
    
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeSliders();
    });
    
    // Also initialize on window load as backup
    window.addEventListener('load', function() {
        initializeSliders();
    });
    
    function initializeSliders() {
        // Check if slider container exists
        const sliderContainer = document.getElementById('heroSlider1');
        if (!sliderContainer) {
            console.log('Slider container not found');
            return;
        }
        
        // Count slider items
        const sliderItems = sliderContainer.querySelectorAll('.hero-slider');
        console.log('Found ' + sliderItems.length + ' slider items');
        
        if (sliderItems.length === 0) {
            console.log('No slider items found');
            return;
        }
        
        // If only one slide, hide navigation
        if (sliderItems.length === 1) {
            hideSliderNavigation();
        }
        
        // Ensure all slides are properly sized
        sliderItems.forEach(function(slide, index) {
            slide.style.minHeight = '500px';
            slide.setAttribute('data-slide-index', index);
        });
        
        // Initialize slider if using Slick
        if (typeof $ !== 'undefined' && $.fn.slick) {
            initializeSlickSlider();
        }
        
        // Initialize slider if using Swiper
        if (typeof Swiper !== 'undefined') {
            initializeSwiperSlider();
        }
        
        // Initialize custom slider
        initializeCustomSlider();
    }
    
    function hideSliderNavigation() {
        // Hide arrows
        const arrows = document.querySelectorAll('.slick-arrow, .slider-arrow, .hero-arrow');
        arrows.forEach(function(arrow) {
            arrow.style.display = 'none';
        });
        
        // Hide dots
        const dots = document.querySelectorAll('.slick-dots, .slider-dots, .hero-dots');
        dots.forEach(function(dot) {
            dot.style.display = 'none';
        });
    }
    
    function initializeSlickSlider() {
        const $slider = $('#heroSlider1');
        
        // Destroy existing slider if it exists
        if ($slider.hasClass('slick-initialized')) {
            $slider.slick('unslick');
        }
        
        // Initialize Slick slider
        $slider.slick({
            dots: true,
            arrows: true,
            infinite: true,
            speed: 500,
            fade: true,
            cssEase: 'linear',
            autoplay: true,
            autoplaySpeed: 5000,
            pauseOnHover: true,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        arrows: false,
                        dots: true
                    }
                }
            ]
        });
        
        console.log('Slick slider initialized');
    }
    
    function initializeSwiperSlider() {
        const swiperContainer = document.querySelector('#heroSlider1');
        if (!swiperContainer) return;
        
        new Swiper(swiperContainer, {
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            }
        });
        
        console.log('Swiper slider initialized');
    }
    
    function initializeCustomSlider() {
        const sliderContainer = document.getElementById('heroSlider1');
        const slides = sliderContainer.querySelectorAll('.hero-slider');
        
        if (slides.length <= 1) return;
        
        let currentSlide = 0;
        
        // Hide all slides except first
        slides.forEach(function(slide, index) {
            if (index === 0) {
                slide.style.display = 'block';
                slide.classList.add('active');
            } else {
                slide.style.display = 'none';
                slide.classList.remove('active');
            }
        });
        
        // Auto-advance slides
        setInterval(function() {
            // Hide current slide
            slides[currentSlide].style.display = 'none';
            slides[currentSlide].classList.remove('active');
            
            // Move to next slide
            currentSlide = (currentSlide + 1) % slides.length;
            
            // Show next slide
            slides[currentSlide].style.display = 'block';
            slides[currentSlide].classList.add('active');
        }, 5000);
        
        console.log('Custom slider initialized with ' + slides.length + ' slides');
    }
    
    // Debug function to check slider status
    window.debugSlider = function() {
        const container = document.getElementById('heroSlider1');
        if (!container) {
            console.log('❌ Slider container not found');
            return;
        }
        
        const slides = container.querySelectorAll('.hero-slider');
        console.log('✅ Slider container found');
        console.log('📊 Number of slides:', slides.length);
        
        slides.forEach(function(slide, index) {
            const bgImage = slide.style.backgroundImage;
            const title = slide.querySelector('.hero-subtitle');
            const desc = slide.querySelector('.hero-title');
            
            console.log(`Slide ${index + 1}:`);
            console.log('  - Background:', bgImage ? '✅ Set' : '❌ Missing');
            console.log('  - Title:', title ? title.textContent.trim() : '❌ Missing');
            console.log('  - Description:', desc ? desc.textContent.trim() : '❌ Missing');
        });
        
        // Check for JavaScript libraries
        console.log('📚 Available libraries:');
        console.log('  - jQuery:', typeof $ !== 'undefined' ? '✅' : '❌');
        console.log('  - Slick:', typeof $.fn.slick !== 'undefined' ? '✅' : '❌');
        console.log('  - Swiper:', typeof Swiper !== 'undefined' ? '✅' : '❌');
    };
    
})();
