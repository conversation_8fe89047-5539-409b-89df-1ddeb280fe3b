<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class UpdateWebSlidersAddStatusType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('web_sliders', function (Blueprint $table) {
            // Add Status column if it doesn't exist
            if (!Schema::hasColumn('web_sliders', 'Status')) {
                $table->integer('Status')->default(1)->after('id');
            }

            // Add Type column if it doesn't exist
            if (!Schema::hasColumn('web_sliders', 'Type')) {
                $table->integer('Type')->default(0)->after('Image');
            }
        });

        // Update existing records to have proper values
        DB::table('web_sliders')->whereNull('Status')->update(['Status' => 1]);
        DB::table('web_sliders')->whereNull('Type')->update(['Type' => 0]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('web_sliders', function (Blueprint $table) {
            //
        });
    }
}
