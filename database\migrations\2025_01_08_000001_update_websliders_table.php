<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class UpdateWebslidersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if web_sliders table exists, if not create it
        if (!Schema::hasTable('web_sliders')) {
            Schema::create('web_sliders', function (Blueprint $table) {
                $table->id();
                $table->integer('Status')->default(1);
                $table->longText('Arabic_Title')->nullable();
                $table->longText('English_Title')->nullable();
                $table->longText('Arabic_Desc')->nullable();
                $table->longText('English_Desc')->nullable();
                $table->longText('Image')->nullable();
                $table->integer('Type')->default(0);
                $table->timestamps();
            });
        } else {
            // If table exists, add missing columns
            Schema::table('web_sliders', function (Blueprint $table) {
                // Add Status column if it doesn't exist
                if (!Schema::hasColumn('web_sliders', 'Status')) {
                    $table->integer('Status')->default(1)->after('id');
                }

                // Ensure Type column exists and has proper default
                if (!Schema::hasColumn('web_sliders', 'Type')) {
                    $table->integer('Type')->default(0)->after('Image');
                }
            });

            // Update existing records to have proper Status and Type values
            if (Schema::hasColumn('web_sliders', 'Status')) {
                DB::statement('UPDATE web_sliders SET Status = 1 WHERE Status IS NULL');
            }
            if (Schema::hasColumn('web_sliders', 'Type')) {
                DB::statement('UPDATE web_sliders SET Type = 0 WHERE Type IS NULL');
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Don't drop the table or columns to prevent data loss
        // Schema::dropIfExists('web_sliders');
    }
}
