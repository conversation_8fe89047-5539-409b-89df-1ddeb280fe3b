<?php $__env->startSection('content'); ?>

<title>
<?php echo e(app()->getLocale() == 'ar' ?$Def->Name :$Def->NameEn); ?>

</title>


  <!--==============================
    slider Area
    ==============================-->
    <div class="hero-wrapper hero-1" id="hero">
        <div class="global-carousel" id="heroSlider1" data-fade="true" data-slide-show="1" data-lg-slide-show="1" data-md-slide-show="1" data-sm-slide-show="1" data-xs-slide-show="1" data-arrows="true" data-xl-arrows="true" data-ml-arrows="true">


               <?php $__empty_1 = true; $__currentLoopData = $Webslider; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="hero-slider" style="background-image: url(<?php echo e(URL::to($slide->Image)); ?>); background-size: cover; background-position: center; background-repeat: no-repeat;">
                <div class="hero-shape1 shape-mockup movingX" data-bottom="165px" data-right="0">
                    <img src="assets/img/" alt="img">
                </div>
                <div class="container">
                    <div class="row sectionOne">
                        <div class="col-xl-6 col-lg-7 col-md-9">
                            <div class="hero-style1">
                                <span class="hero-subtitle" data-ani="slideinup" data-ani-delay="0s"><?php echo e(app()->getLocale() == 'ar' ?$slide->Arabic_Title :$slide->English_Title); ?></span>
                                <h1 class="hero-title text-white" data-ani="slideinup" data-ani-delay="0.1s">
                                     <?php echo e(app()->getLocale() == 'ar' ?$slide->Arabic_Desc :$slide->English_Desc); ?>

                                </h1>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <!-- Fallback slide if no sliders found -->
            <div class="hero-slider" style="background-image: url('assets/img/default-slider.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
                <div class="container">
                    <div class="row sectionOne">
                        <div class="col-xl-6 col-lg-7 col-md-9">
                            <div class="hero-style1">
                                <span class="hero-subtitle" data-ani="slideinup" data-ani-delay="0s">Welcome</span>
                                <h1 class="hero-title text-white" data-ani="slideinup" data-ani-delay="0.1s">
                                     No sliders found
                                </h1>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        </div>
        <div class="hero-arrow">
            <button data-slick-prev="#heroSlider1" class="slick-arrow slick-prev">PREV</button>
            <button data-slick-next="#heroSlider1" class="slick-arrow slick-next">NEXT</button>
        </div>
    </div>
    <!--======== / Hero Section ========-->

    <!--==============================
    feature Area
    ==============================-->
    <div class="space">
        <div class="container">
            <div class="feature-area">
                <div class="row gx-0">

                  <?php $__currentLoopData = $Features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>


                    <div class="col-lg-3">
                        <div class="feature-card">
                            <div class="feature-card_bg">
                                <img src="<?php echo e(asset('Front/assets/img/bg/feature-card_bg1.png')); ?>" alt="img">

                            </div>
                            <div class="feature-card_icon">
                                <img src="<?php echo e(URL::to($feat->Icon)); ?>" alt="img">

                            </div>
                            <h6 class="feature-card_subtitle">

                    <?php echo e(app()->getLocale() == 'ar' ?$feat->Arabic_Title :$feat->English_Title); ?>

                            </h6>


                        </div>
                    </div>
                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>

    <!--==============================
    About Area
    ==============================-->
    <div class="space-bottom" id="About">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="about-thumb mb-5 mb-lg-0">
                        <img class="about-img-1" src=" <?php echo e(URL::to($About->Image)); ?>" alt="img">
                        <img class="about-img-2 jump" src="<?php echo e(asset('Front/assets/img/aboutt.png')); ?>" alt="img">
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="about-content-wrap">
                        <div class="title-area mb-0">
                            <span class="sub-title"><?php echo e(trans('admin.About')); ?></span>
                            <h2 class="sec-title"> <?php echo e(app()->getLocale() == 'ar' ?$About->Arabic_Title :$About->English_Title); ?>  </h2>
                            <p class="sec-text">     <?php echo e(app()->getLocale() == 'ar' ?$About->Arabic_Desc :$About->English_Desc); ?>

                            </p>

                        </div>
                        <div class="btn-wrap mt-40">

                            <div class="about-info-wrap">
                                <div class="icon"><i class="fas fa-phone-volume"></i></div>
                                <div class="details">
                                    <p class="about-info-title"><?php echo e(trans('admin.NeedHelp?')); ?></p>
                                    <a class="about-info-link" href="tel:<?php echo e($Footer->Phone); ?>"><?php echo e($Footer->Phone); ?></a>
                                </div>
                            </div>





                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <!--==============================
    Shop Area
    ==============================-->
             <div class="ShopArea">
                <div class="row justify-content-center">
                    <div class="col-xl-6 col-lg-8 text-center">
                        <div class="title-area text-center">
                            <span class="sub-title text-theme2"><?php echo e(trans('admin.OurGoods')); ?></span>

                        </div>
                    </div>
                </div>
                <div class="row global-carousel" id="productCarousel" data-slide-show="4" data-lg-slide-show="4" data-md-slide-show="3" data-sm-slide-show="2" data-xs-slide-show="1">

                          <?php $__currentLoopData = $Products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-3 col-md-6">
                        <div class="product-card">
                            <div class="product-img">
                                <img src="<?php echo e(URL::to($pro->Image)); ?>" alt="Product Image">
                                <div class="actions">
                                    <a href="<?php echo e(url('ProDetails/'.$pro->id)); ?>" class="btn style2"><i class="fal fa-shopping-cart"></i></a>
                                    <a href="<?php echo e(url('PrescriptionPro/'.$pro->id)); ?>" class="btn style2"><i class="fas fa-file"></i></a>

                                </div>


                                                         <?php if($pro->Type == 0): ?>


                                   <div class="product-tag"> <?php echo e(trans('admin.Recently')); ?></div>
                                          <?php elseif($pro->Type == 1): ?>

                                   <div class="product-tag"> <?php echo e(trans('admin.Special')); ?></div>
                                          <?php elseif($pro->Type == 2): ?>


                                   <div class="product-tag"><?php echo e(trans('admin.Finaly')); ?></div>

                                         <?php endif; ?>
                            </div>
                            <div class="product-content">
                                <h3 class="product-title"><a href="<?php echo e(url('ProDetails/'.$pro->id)); ?>"> <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?></a></h3>

                                   <?php if(!empty($pro->Offer_Price)): ?>
                                <span class="price"><del><?php echo e($pro->Price); ?></del><?php echo e($pro->Offer_Price); ?> <?php echo e($pro->Symbol); ?></span>
                                     <?php else: ?>
                                   <span class="price"><?php echo e($pro->Price); ?> <?php echo e($pro->Symbol); ?></span>
                                    <?php endif; ?>
                            </div>
                        </div>
                    </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>



    <!--==============================
    Service Area
    ==============================-->
    <div class="service-bg-area" data-bg-src="<?php echo e(asset('Front/assets/img/bg/service-bg.png')); ?>">
        <div class="sec-shape-top">
            <img src="<?php echo e(asset('Front/assets/img/bg/sec-shape-top.png')); ?>" alt="img">
        </div>
        <!--==============================
        Service Area 01
        ==============================-->
        <div class="service-area-1  overflow-hidden">
            <div class="container">
                <div class="title-area text-center">
                <span class="sub-title"><?php echo e(trans('admin.Services')); ?></span>

            </div>
            </div>
            <div class="container-fluid p-0">
                <div class="row global-carousel service-slider-1" data-slide-show="4" data-ml-slide-show="3" data-lg-slide-show="3" data-md-slide-show="2" data-sm-slide-show="1" data-xs-slide-show="1" data-dots="false">




   <?php $__currentLoopData = $Services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $serv): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                    <div class="col-lg-4 col-md-6">
                        <div class="service-card">
                            <div class="service-card_icon">
                                <img src="<?php echo e(URL::to($serv->Icon)); ?>" alt="img">
                            </div>
                            <div class="service-card_content">
                                <h4 class="service-card_title h5"><a href="#"><?php echo e(app()->getLocale() == 'ar' ?$serv->Arabic_Title :$serv->English_Title); ?>   </a></h4>
                                <p class="service-card_text">  <?php echo e(app()->getLocale() == 'ar' ?$serv->Arabic_Desc :$serv->English_Desc); ?>  </p>

                            </div>
                        </div>
                    </div>

                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                </div>
            </div>
        </div>

        <div class="sec-shape-bottom">
            <img src="<?php echo e(asset('Front/assets/img/bg/sec-shape-bottom.png')); ?>" alt="img">
        </div>
    </div>




    <!-----portfolio start----->
       <div class="portfolio-area-1" style="background-image:url(<?php echo e(asset('Front/assets/img/bg/portfolio-bg-1.png')); ?>); background-size: cover;">

        <div class="container">
            <div class="title-area text-center">
                <span class="sub-title"> <?php echo e(trans('admin.Portfilio')); ?></span>

            </div>
        </div>
        <div class="container-fluid">
            <div class="flip-gallery-area">
                <div class="flip-gallery">
                    <ul class="flip-items">

    <?php $__currentLoopData = $Gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gal): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                        <li>
                            <div class="gallery-card gallery-flip">
                                <div class="gallery-img">
                                    <img src="<?php echo e(URL::to($gal->Image)); ?>" alt="gallery image">
                                </div>
                                <div class="gallery-content">
                                    <div class="media-left">
                                        <h6 class="gallery-content_subtitle">

                                          <?php if(!empty($gal->Category()->first()->Arabic_Name)): ?>

                           <?php echo e(app()->getLocale() == 'ar' ?$gal->Category()->first()->Arabic_Name :$gal->Category()->first()->English_Name); ?>

                                            <?php endif; ?>
                                        </h6>
                                        <h4 class="gallery-content_title"><?php echo e(app()->getLocale() == 'ar' ?$gal->Arabic_Name :$gal->English_Name); ?></h4>
                                    </div>
                                    <a href="<?php echo e($gal->Links); ?>" class="icon-btn popup-image">
                                        <i class="far fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        </li>

                           <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-----portfolio end----->


        <!--==============================
    whu choose us Area
    ==============================-->

    <div class="goal-area-2 space">
        <div class="container">
            <div class="row justify-content-between">
                <div class="col-xl-6 align-self-center order-xl-2">
                    <div class=" mb-xl-0 mb-40">
                        <img src="<?php echo e(URL::to($HowWeWork->Image)); ?>" alt="img">
                    </div>
                </div>
                <div class="col-xl-6 order-xl-1">
                    <div class="title-area">
                        <span class="sub-title style2"> <?php echo e(trans('admin.WhyChoose')); ?></span>
                        <h2 class="sec-title fw-bold"><?php echo e(app()->getLocale() == 'ar' ?$HowWeWork->Arabic_Title :$HowWeWork->English_Title); ?></h2>

                        <div class="about-grid-wrap style3 mt-40">
                            <div class="about-grid style3">
                                <div class="about-grid_icon">
                                    <img src="<?php echo e(URL::to($HowWeWork1->Icon)); ?>" alt="img">
                                </div>
                                <div class="about-grid_content">

                                    <p class="about-grid_text"><?php echo e(app()->getLocale() == 'ar' ?$HowWeWork1->Arabic_Title :$HowWeWork1->English_Title); ?></p>
                                </div>
                            </div>
                            <div class="about-grid style3">
                                <div class="about-grid_icon">
                           <img src="<?php echo e(URL::to($HowWeWork2->Icon)); ?>" alt="img">

                                </div>
                                <div class="about-grid_content">

                                    <p class="about-grid_text"><?php echo e(app()->getLocale() == 'ar' ?$HowWeWork2->Arabic_Title :$HowWeWork2->English_Title); ?></p>
                                </div>
                            </div>
                                 <div class="about-grid style3">
                                <div class="about-grid_icon">
                                    <img src="<?php echo e(URL::to($HowWeWork3->Icon)); ?>" alt="img">

                                </div>
                                <div class="about-grid_content">

                                    <p class="about-grid_text"><?php echo e(app()->getLocale() == 'ar' ?$HowWeWork3->Arabic_Title :$HowWeWork3->English_Title); ?></p>
                                </div>
                            </div>
                            <div class="about-grid style3">
                                <div class="about-grid_icon">
                                    <img src="<?php echo e(URL::to($HowWeWork4->Icon)); ?>" alt="img">

                                </div>
                                <div class="about-grid_content">

                                    <p class="about-grid_text"><?php echo e(app()->getLocale() == 'ar' ?$HowWeWork4->Arabic_Title :$HowWeWork4->English_Title); ?></p>
                                </div>
                            </div>


                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

      <!--==============================



    Testimonial Area 02
    ==============================-->
    <div class="testimonial-area-2  overflow-hidden">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="title-area text-center">
                        <span class="sub-title style2">S <?php echo e(trans('admin.Testimonial')); ?> </span>

                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="testi-box-wrap2 text-center">
                        <div class="row global-carousel testi-slider-2" id="testiSlider2" data-slide-show="1">

   <?php $__currentLoopData = $Testiminoals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testi): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-6">
                                <div class="testi-box style2">
                                    <div class="testi-box_thumb">
                                        <img src="<?php echo e(URL::to($testi->Image)); ?>" alt="img">
                                    </div>
                                    <div class="testi-box_content">
                                        <p class="testi-box_text"> <?php echo e(app()->getLocale() == 'ar' ?$testi->Arabic_Desc :$testi->English_Desc); ?>   </p>
                                    </div>
                                    <div class="testi-box_profile">
                                        <h4 class="testi-box_name">  <?php echo e(app()->getLocale() == 'ar' ?$testi->Arabic_Name :$testi->English_Name); ?></h4>
                                        <span class="testi-box_desig"><?php echo e(app()->getLocale() == 'ar' ?$testi->Arabic_Job :$testi->English_Job); ?> </span>
                                    </div>
                                </div>
                            </div>
                             <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        <div class="testi-arrow">
                            <button data-slick-prev="#testiSlider2" class="slick-arrow slick-prev"><i class="fa-light fa-arrow-left"></i></button>
                            <button data-slick-next="#testiSlider2" class="slick-arrow slick-next"><i class="fa-light fa-arrow-right"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!--==============================
    Blog Area
    ==============================-->
    <section class="blog-area mt-50 mb-50 bg-smoke3">
        <div class="container">
            <div class="title-area text-center" style="padding: 20px">
                <span class="sub-title">
                     <?php echo e(trans('admin.Blogs')); ?>

                </span>

            </div>
            <div class="row global-carousel blog-slider" data-slide-show="3" data-lg-slide-show="2" data-md-slide-show="2" data-sm-slide-show="1" data-xs-slide-show="1" data-dots="false" data-md-dots="true">



     <?php $__currentLoopData = $Articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $art): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>


                <div class="col-md-6 col-lg-4">
                    <div class="blog-card">
                        <div class="blog-img">
                            <img src="<?php echo e(URL::to($art->Sub_Image)); ?>" alt="blog image">
                        </div>
                        <div class="blog-content" data-bg-src="<?php echo e(asset('Front/assets/img/blog/blog_card1_bg.png')); ?>">
                            <div class="blog-meta">
                                <a href="<?php echo e(url('BlogsDet/'.$art->id)); ?>"><i class="fal fa-calendar"></i><?php echo e($art->Date); ?></a>
                                <a href="<?php echo e(url('BlogsDet/'.$art->id)); ?>"><i class="far fa-user"></i><?php echo e($art->Author); ?></a>
                            </div>
                            <h3 class="blog-title box-title"><a href="<?php echo e(url('BlogsDet/'.$art->id)); ?>">  <?php echo e(app()->getLocale() == 'ar' ?$art->Arabic_Title :$art->English_Title); ?>  </a></h3>

                        </div>
                    </div>
                </div>

                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>
        </div>
    </section>





<?php $__env->stopSection(); ?>

<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/site/home.blade.php ENDPATH**/ ?>