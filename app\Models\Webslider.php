<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Webslider extends Model
{
    use HasFactory;

    protected $table = 'websliders'; // Fixed table name to match actual database table

    protected $fillable = [
        'Status',
        'Arabic_Title',
        'English_Title',
        'Arabic_Desc',
        'English_Desc',
        'Image',
        'Type',
    ];

    protected $casts = [
        'Status' => 'integer',
        'Type' => 'integer',
    ];

    // Scope for active sliders
    public function scopeActive($query)
    {
        return $query->where('Status', 1);
    }

    // Scope for website sliders
    public function scopeWebsite($query)
    {
        return $query->where(function($q) {
            $q->where('Type', 0)->orWhereNull('Type');
        });
    }

    // Scope for mobile sliders
    public function scopeMobile($query)
    {
        return $query->where('Type', 1);
    }
}
