<?php
use App\Models\CompanyData;
use App\Models\Footer;
use App\Models\SocialMedia;
$Def=CompanyData::orderBy('id','desc')->first();
$Footer=Footer::orderBy('id','desc')->first();
$Social=SocialMedia::orderBy('id','desc')->first();
?>


    <!--==============================
        Footer Area
    ==============================-->
    <footer class="footer-wrapper footer-layout1" data-bg-src="assets/img/bg/footer-1-bg.png">
        <div class="container">


            <div class="widget-area">
                <div class="row justify-content-between">
                    <div class="col-md-6 col-xl-3">
                        <div class="widget footer-widget">
                            <div class="widget-about">
                                <div class="footer-logo">

                                  <a href="<?php echo e(url('/')); ?>"><img src="<?php echo e(URL::to($Def->Logo_Store)); ?>" alt="" style="height:130px;"></a>
                                </div>
                                <p class="about-text">
                       <?php echo e(app()->getLocale() == 'ar' ?$Footer->Arabic_Desc :$Footer->English_Desc); ?>

                                </p>

                                <div class="social-btn">

                                    <?php if(!empty($Social->Facebook)): ?>
                                  <a href="<?php echo e($Social->Facebook); ?>" title="Facebook" target="_blank"><i class="fab fa-facebook-f"></i></a>
                                  <?php endif; ?>

                                        <?php if(!empty($Social->Twitter)): ?>
                                  <a href="<?php echo e($Social->Twitter); ?>" title="Facebook" target="_blank"><i class="fab fa-twitter"></i></a>
                                  <?php endif; ?>

                                        <?php if(!empty($Social->Youtube)): ?>
                                  <a href="<?php echo e($Social->Youtube); ?>" title="Facebook" target="_blank"><i class="fab fa-youtube"></i></a>
                                  <?php endif; ?>


                                        <?php if(!empty($Social->LinkedIn)): ?>
                                  <a href="<?php echo e($Social->LinkedIn); ?>" title="Facebook" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                                  <?php endif; ?>



                                        <?php if(!empty($Social->Instagram)): ?>
                                  <a href="<?php echo e($Social->Instagram); ?>" title="Facebook" target="_blank"><i class="fab fa-instagram"></i></a>
                                  <?php endif; ?>


                                        <?php if(!empty($Social->Snapchat)): ?>
                                  <a href="<?php echo e($Social->Snapchat); ?>" title="Facebook" target="_blank"><i class="fab fa-snapchat"></i></a>
                                  <?php endif; ?>


                                        <?php if(!empty($Social->Whatsapp)): ?>
                                  <a href="https://wa.me/<?php echo e($Social->Whatsapp); ?>" title="Facebook" target="_blank"><i class="fab fa-whatsapp"></i></a>
                                  <?php endif; ?>


                                        <?php if(!empty($Social->Google_Plus)): ?>
                                  <a href="<?php echo e($Social->Google_Plus); ?>" title="Facebook" target="_blank"><i class="fab fa-google-plus-g"></i></a>
                                  <?php endif; ?>


                                        <?php if(!empty($Social->Pinterest)): ?>
                                  <a href="<?php echo e($Social->Pinterest); ?>" title="Facebook" target="_blank"><i class="fab fa-pinterest"></i></a>
                                  <?php endif; ?>


                                        <?php if(!empty($Social->Telegram)): ?>
                                  <a href="https://t.me/<?php echo e($Social->Telegram); ?>" title="Facebook" target="_blank"><i class="fab fa-telegram"></i></a>
                                  <?php endif; ?>


                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-xl-auto">
                        <div class="widget widget_nav_menu footer-widget">
                            <h3 class="widget_title"><?php echo e(trans('admin.Pages')); ?></h3>
                            <div class="menu-all-pages-container">
                                <ul class="menu">
                            <li><a href="<?php echo e(url('/')); ?>"><?php echo e(trans('admin.Home')); ?></a></li>
                              <li><a href="#About"><?php echo e(trans('admin.About')); ?></a></li>
                              <li><a href="<?php echo e(url('ShopSite')); ?>"><?php echo e(trans('admin.Shop')); ?></a></li>
                              <li><a href="<?php echo e(url('PolicySite')); ?>"><?php echo e(trans('admin.Policy')); ?> </a></li>
                              <li><a href="<?php echo e(url('TermsSite')); ?>"><?php echo e(trans('admin.Terms')); ?> </a></li>
                              <li><a href="<?php echo e(url('FAQSite')); ?>"><?php echo e(trans('admin.FAQ')); ?></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-xl-auto">
                        <div class="widget footer-widget">
                            <h3 class="widget_title"><?php echo e(trans('admin.Location')); ?></h3>


                            <?php echo $Footer->Map; ?>


                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="copyright-wrap">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-auto align-self-center">
                        <p class="copyright-text text-center">© <?php echo e(date('Y')); ?> <a href="https://klarapps.com">KLAR.</a> All Rights Reserved.</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>




    <!--********************************
			Code End  Here
	******************************** -->

    <!-- Scroll To Top -->
    <div class="scroll-top">
        <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
            <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" style="transition: stroke-dashoffset 10ms linear 0s; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 307.919;"></path>
        </svg>
    </div>

    <!--==============================
    All Js File
    ============================== -->
    <!-- Jquery -->
    <script src="<?php echo e(asset('Front/assets/js/vendor/jquery-3.6.0.min.js')); ?>"></script>
    <!-- Slick Slider -->
    <script src="<?php echo e(asset('Front/assets/js/slick.min.js')); ?>"></script>
    <!-- Bootstrap -->
    <script src="<?php echo e(asset('Front/assets/js/bootstrap.min.js')); ?>"></script>
    <!-- Magnific Popup -->
    <script src="<?php echo e(asset('Front/assets/js/jquery.magnific-popup.min.js')); ?>"></script>
    <!-- Counter Up -->
    <script src="<?php echo e(asset('Front/assets/js/jquery.counterup.min.js')); ?>"></script>
    <!-- Range Slider -->
    <script src="<?php echo e(asset('Front/assets/js/jquery-ui.min.js')); ?>"></script>
        <script src="<?php echo e(asset('Front/assets/js/jquery.flipster.min.js')); ?>"></script>

    <!-- Isotope Filter -->
    <script src="<?php echo e(asset('Front/assets/js/imagesloaded.pkgd.min.js')); ?>"></script>
    <script src="<?php echo e(asset('Front/assets/js/isotope.pkgd.min.js')); ?>"></script>

    <!-- Main Js File -->
    <script src="<?php echo e(asset('Front/assets/js/main.js')); ?>"></script>
</body>

</html>



<?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/site/layouts/footer.blade.php ENDPATH**/ ?>