@include('site.layouts.header')
@include('site.layouts.navbar')

   @yield('content')
     <span id="ex"> @include('admin.layouts.messages')</span>
@include('site.layouts.footer')
<style>
    .tp-rightarrow{
        display: none !important;
    }
    .tp-leftarrow{
            display: none !important;
    }
</style>
   <link rel="preconnect" href="https://fonts.googleapis.com">

    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Alexandria:wght@200;300;400;500;800;1000&family=Alexandria:wght@300;500;700&family=Alexandria:wght@200;500;800;900&display=swap" rel="stylesheet">

    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Alexandria', sans-serif  !important;
        }

        /* Override Bootstrap blue color with shonashrom brown color */
        .btn-primary, .bg-primary {
            background-color: #5c4b2d !important;
            border-color: #5c4b2d !important;
        }

        .btn-primary:hover, .btn-primary:focus, .btn-primary:active {
            background-color: #4a3d24 !important;
            border-color: #4a3d24 !important;
        }

        .text-primary {
            color: #5c4b2d !important;
        }

        .border-primary {
            border-color: #5c4b2d !important;
        }

        /* Override any remaining blue colors */
        *[style*="#0d6efd"] {
            background-color: #5c4b2d !important;
        }

        /* Global RTL/LTR Support */
        .rtl-layout {
            direction: rtl !important;
            text-align: right;
        }

        .ltr-layout {
            direction: ltr !important;
            text-align: left;
        }

        /* RTL specific adjustments */
        .rtl-layout .container,
        .rtl-layout .row {
            direction: rtl;
        }

        .rtl-layout .navbar-nav {
            flex-direction: row-reverse;
        }

        .rtl-layout .dropdown-menu {
            right: 0;
            left: auto;
        }

        .rtl-layout .text-left {
            text-align: right !important;
        }

        .rtl-layout .text-right {
            text-align: left !important;
        }

        .rtl-layout .float-left {
            float: right !important;
        }

        .rtl-layout .float-right {
            float: left !important;
        }

        .rtl-layout .ml-auto {
            margin-left: 0 !important;
            margin-right: auto !important;
        }

        .rtl-layout .mr-auto {
            margin-right: 0 !important;
            margin-left: auto !important;
        }

        /* Force center alignment for specific elements */
        .rtl-layout .text-center,
        .ltr-layout .text-center,
        .rtl-layout .breadcumb-content,
        .ltr-layout .breadcumb-content,
        .rtl-layout .section-title,
        .ltr-layout .section-title {
            text-align: center !important;
        }

        /* Arabic font support */
        .rtl-layout {
            font-family: 'Cairo', 'Tajawal', 'Alexandria', sans-serif !important;
        }

        .ltr-layout {
            font-family: 'Roboto', 'Open Sans', 'Alexandria', sans-serif !important;
        }

        /* Enhanced Direction Support */
        .rtl-layout * {
            direction: inherit;
        }

        .ltr-layout * {
            direction: inherit;
        }

        /* Override for specific content that should maintain direction */
        .rtl-layout .breadcumb-title,
        .ltr-layout .breadcumb-title {
            text-align: center !important;
        }

        /* Ensure theme color consistency */
        :root {
            --bs-primary: #5c4b2d !important;
            --bs-primary-rgb: 92, 75, 45 !important;
        }

        /* Fix pagination colors - only active page */
        .pagination .page-item.active .page-link {
            background-color: #5c4b2d !important;
            border-color: #5c4b2d !important;
            color: white !important;
        }
    </style>

@php
$main = new stdClass();
$main->Font_Type = 1; // Default font type
@endphp

@if($main->Font_Type == 1)
    <!-- Default font already loaded above -->
@elseif($main->Font_Type == 2)
     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Mada:wght@200;300;400;500;800;1000&family=Mada:wght@300;500;700&family=Mada:wght@200;500;800;900&display=swap" rel="stylesheet">

    <style>
        body  , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Mada', sans-serif  !important;
        }
    </style>

@elseif($main->Font_Type == 3)
   <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Alexandria:wght@200;300;400;500;800;1000&family=Alexandria:wght@300;500;700&family=Alexandria:wght@200;500;800;900&display=swap" rel="stylesheet">

    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Alexandria', sans-serif  !important;
        }
    </style>

@elseif($main->Font_Type == 4)
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,700;1,400;1,700&family=Marhey:wght@200;300;400;500;800;1000&family=Marhey:wght@300;500;700&family=Marhey:wght@200;500;800;900&display=swap" rel="stylesheet">

    <style>
        body , h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Marhey', sans-serif  !important;
        }
    </style>

@elseif($main->Font_Type == 5)
     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Reem+Kufi+Fun:ital,wght@0,700;1,400;1,700&family=Reem+Kufi+Fun:wght@200;300;400;500;800;1000&family=Reem+Kufi+Fun:wght@300;500;700&family=Reem+Kufi+Fun:wght@200;500;800;900&display=swap" rel="stylesheet">

    <style>
        body, h1,h2,h3,h4,h5,h6,div,span,strong,p,a,th,button {
            font-family: 'Reem Kufi Fun', sans-serif !important;
        }
    </style>

@endif
