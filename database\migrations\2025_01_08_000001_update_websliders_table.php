<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateWebslidersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('websliders', function (Blueprint $table) {
            // Add Status column if it doesn't exist
            if (!Schema::hasColumn('websliders', 'Status')) {
                $table->integer('Status')->default(1)->after('id');
            }
            
            // Ensure Type column exists and has proper default
            if (!Schema::hasColumn('websliders', 'Type')) {
                $table->integer('Type')->default(0)->after('Image');
            } else {
                // Update existing Type column to have proper default
                $table->integer('Type')->default(0)->change();
            }
        });
        
        // Update existing records to have proper Status and Type values
        DB::statement('UPDATE websliders SET Status = 1 WHERE Status IS NULL');
        DB::statement('UPDATE websliders SET Type = 0 WHERE Type IS NULL');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('websliders', function (Blueprint $table) {
            // Don't drop columns in down method to prevent data loss
            // $table->dropColumn(['Status']);
        });
    }
}
